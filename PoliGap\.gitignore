# Dependencies
node_modules/

# Build outputs
dist/
dist-ssr/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*

# Coverage directory
coverage/

# Cache directories
.cache
.parcel-cache
.npm
.eslintcache

# TypeScript cache
*.tsbuildinfo

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
._*
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Package manager files (keep package-lock.json for consistency)
# yarn.lock
# pnpm-lock.yaml
